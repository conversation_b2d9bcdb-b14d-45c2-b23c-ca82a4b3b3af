import { useGSAP } from '@gsap/react'
import gsap from 'gsap'
import { useRef } from 'react'

function LandingPage() {
    const containerRef = useRef();

    useGSAP(() => {
        const tl = gsap.timeline();
        const cards = gsap.utils.toArray('.card');

        cards.forEach((card, i) => {
            tl.from(card, {
                y: 240,
                duration: 1,
                ease: 'expoScale(0.5,7,none)',
                opacity: 0
            }, i * 0.3); // stagger by 0.3 seconds
        });
    }, []);

    return (
        <div className='w-full relative z-[99] h-full'>
            <div ref={containerRef} className='h-full'>
                {/* Laptop Layout - 3 cards with better spacing */}
                <div className="hidden lg:block xl:hidden">
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[280px] h-[420px] bg-white/60 backdrop-blur-md shadow-xl p-4 bg-[url('/img/bg.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-4'>
                            <h1 className='text-lg font-bold leading-none'>Quick-fire <br /> transactions</h1>
                            <p className='font-bold text-base'>02</p>
                            <p className='text-sm'>Use Huobi for secure and efficient cryptocurrency transactions, ensuring your transactions are conducted safely and swiftly.</p>
                        </div>
                    </div>
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -translate-x-[320px] w-[280px] h-[420px] bg-white/60 backdrop-blur-md shadow-xl p-4 bg-[url('https://i.pinimg.com/736x/42/45/85/4245858f91bbdf023ef06c9014582b34.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-4'>
                            <h1 className='text-lg font-bold leading-none'>Fast & Reliable <br /> Transfers</h1>
                            <p className='font-bold text-base'>01</p>
                            <p className='text-sm'>Choose Huobi for fast, secure, and seamless cryptocurrency transactions—experience peace of mind with every trade.</p>
                        </div>
                    </div>
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 translate-x-[320px] w-[280px] h-[420px] bg-white/60 backdrop-blur-md shadow-xl p-4 bg-[url('/img/bg3.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-4'>
                            <h1 className='text-lg font-bold leading-none'>Swift Digital <br /> Exchanges</h1>
                            <p className='font-bold text-base'>03</p>
                            <p className='text-sm'>Trust Huobi for seamless and secure crypto transactions—where speed meets reliability.</p>
                        </div>
                    </div>
                </div>

                {/* Large Desktop Layout - 3 cards with more spacing */}
                <div className="hidden xl:block">
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[320px] h-[480px] bg-white/60 backdrop-blur-md shadow-xl p-6 bg-[url('/img/bg.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-6'>
                            <h1 className='text-xl font-bold leading-none'>Quick-fire <br /> transactions</h1>
                            <p className='font-bold text-lg'>02</p>
                            <p className='text-base'>Use Huobi for secure and efficient cryptocurrency transactions, ensuring your transactions are conducted safely and swiftly.</p>
                        </div>
                    </div>
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -translate-x-[380px] w-[320px] h-[480px] bg-white/60 backdrop-blur-md shadow-xl p-6 bg-[url('https://i.pinimg.com/736x/42/45/85/4245858f91bbdf023ef06c9014582b34.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-6'>
                            <h1 className='text-xl font-bold leading-none'>Fast & Reliable <br /> Transfers</h1>
                            <p className='font-bold text-lg'>01</p>
                            <p className='text-base'>Choose Huobi for fast, secure, and seamless cryptocurrency transactions—experience peace of mind with every trade.</p>
                        </div>
                    </div>
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 translate-x-[380px] w-[320px] h-[480px] bg-white/60 backdrop-blur-md shadow-xl p-6 bg-[url('/img/bg3.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-6'>
                            <h1 className='text-xl font-bold leading-none'>Swift Digital <br /> Exchanges</h1>
                            <p className='font-bold text-lg'>03</p>
                            <p className='text-base'>Trust Huobi for seamless and secure crypto transactions—where speed meets reliability.</p>
                        </div>
                    </div>
                </div>

                {/* Tablet Layout - 2 cards */}
                <div className="hidden md:block lg:hidden">
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 -translate-x-[180px] w-[320px] h-[440px] bg-white/60 backdrop-blur-md shadow-xl p-4 bg-[url('/img/bg.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-4'>
                            <h1 className='text-lg font-bold leading-none'>Quick-fire <br /> transactions</h1>
                            <p className='font-bold text-base'>02</p>
                            <p className='text-sm'>Use Huobi for secure and efficient cryptocurrency transactions, ensuring your transactions are conducted safely and swiftly.</p>
                        </div>
                    </div>
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 translate-x-[180px] w-[320px] h-[440px] bg-white/60 backdrop-blur-md shadow-xl p-4 bg-[url('https://i.pinimg.com/736x/42/45/85/4245858f91bbdf023ef06c9014582b34.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[45%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-4'>
                            <h1 className='text-lg font-bold leading-none'>Fast & Reliable <br /> Transfers</h1>
                            <p className='font-bold text-base'>01</p>
                            <p className='text-sm'>Choose Huobi for fast, secure, and seamless cryptocurrency transactions—experience peace of mind with every trade.</p>
                        </div>
                    </div>
                </div>

                {/* Mobile Layout - 1 card centered */}
                <div className="block md:hidden">
                    <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[85vw] max-w-[350px] h-[60vh] min-h-[450px] bg-white/60 backdrop-blur-md shadow-xl p-4 bg-[url('/img/bg.jpg')] bg-cover rounded-lg">
                        <div className='w-full h-[40%]'>
                            <img src="" alt="" />
                        </div>
                        <div className='text-white/90 pt-4'>
                            <h1 className='text-lg font-bold leading-none'>Quick-fire <br /> transactions</h1>
                            <p className='font-bold text-base'>02</p>
                            <p className='text-sm'>Use Huobi for secure and efficient cryptocurrency transactions, ensuring your transactions are conducted safely and swiftly.</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export default LandingPage
