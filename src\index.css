@import "tailwindcss";

/* Global responsive styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#root {
  width: 100%;
  height: 100%;
}

.lightning-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Responsive card animations */
@media (max-width: 768px) {
  .card {
    transition: transform 0.3s ease;
  }

  .card:hover {
    transform: translate(-50%, -50%) scale(1.02) !important;
  }
}

/* Ensure proper touch targets on mobile */
@media (max-width: 640px) {
  .card {
    min-height: 400px;
    padding: 1rem;
  }

  .card h1 {
    font-size: 1.125rem;
    line-height: 1.2;
  }

  .card p {
    font-size: 0.875rem;
    line-height: 1.4;
  }
}