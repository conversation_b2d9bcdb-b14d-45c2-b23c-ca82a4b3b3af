@import "tailwindcss";

/* Global responsive styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow-x: hidden;
}

#root {
  width: 100%;
  height: 100%;
}

.lightning-container {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

/* Laptop specific styles */
@media (min-width: 1024px) and (max-width: 1279px) {
  .card {
    transition: transform 0.3s ease;
  }

  .card:hover {
    transform: translate(-50%, -50%) scale(1.05) !important;
  }
}

/* Large desktop styles */
@media (min-width: 1280px) {
  .card {
    transition: transform 0.3s ease;
  }

  .card:hover {
    transform: translate(-50%, -50%) scale(1.05) !important;
  }
}

/* Tablet styles */
@media (min-width: 768px) and (max-width: 1023px) {
  .card {
    transition: transform 0.3s ease;
  }

  .card:hover {
    transform: translate(-50%, -50%) scale(1.03) !important;
  }
}

/* Mobile styles */
@media (max-width: 767px) {
  .card {
    transition: transform 0.3s ease;
    min-height: 400px;
    padding: 1rem;
  }

  .card:hover {
    transform: translate(-50%, -50%) scale(1.02) !important;
  }

  .card h1 {
    font-size: 1.125rem;
    line-height: 1.2;
  }

  .card p {
    font-size: 0.875rem;
    line-height: 1.4;
  }
}