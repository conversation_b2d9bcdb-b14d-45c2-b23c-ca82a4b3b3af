import { useGSAP } from '@gsap/react'
import gsap from 'gsap'
import React, { useRef } from 'react'

function LandingPage() {
    const containerRef = useRef();

    const tl = gsap.timeline()
    const cards = gsap.utils.toArray('.card')

    useGSAP(() => {
        const tl = gsap.timeline();
        const cards = gsap.utils.toArray('.card');

        cards.forEach((card, i) => {
            tl.from(card, {
                y: 240,
                duration: 1,
                ease: 'expoScale(0.5,7,none)',
                opacity: 0
            }, i * 0.3); // stagger by 0.3 seconds
        });
    }, []);

    return (
        <div className='w-full relative z-[99]'>
            <div ref={containerRef} className=''>
                <div className="card absolute top-1/2 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-[20vw] h-[50vh] bg-white/60 backdrop-blur-md shadow-xl p-2 bg-[url('./img/bg.jpg')] bg-cover">
                    <div className='w-full h-[50%]'>
                        <img src="" alt="" />
                    </div>
                    <div className='text-white/90 pt-6'>
                        <h1 className='text-2xl font-bold leading-none'>Quick-fire <br /> transactions</h1>
                        <p className='font-bold'>02</p>
                        <p className='text-[0.8vw]'>Use Huobi for secure and efficient cryptocurrency transactions, ensureing your transactions are conducted safely and swiftly.</p>
                    </div>
                </div>
                <div className="card absolute top-1/2 left-[23rem] transform -translate-x-1/2 translate-y-1/2 w-[20vw] h-[50vh] bg-white/60 backdrop-blur-md shadow-xl p-2 bg-[url('https://i.pinimg.com/736x/42/45/85/4245858f91bbdf023ef06c9014582b34.jpg')] bg-cover">
                    <div className='w-full h-[50%]'>
                        <img src="" alt="" />
                    </div>
                    <div className='text-white/90 pt-6'>
                        <h1 className='text-2xl font-bold leading-none'>Fast & Reliable <br /> Transfers</h1>
                        <p className='font-bold'>01</p>
                        <p className='text-[0.8vw]'>Choose Huobi for fast, secure, and seamless cryptocurrency transactions—experience peace of mind with every trade.</p>
                    </div>
                </div>
                <div className="card absolute top-1/2 left-[62rem] transform -translate-x-1/2 translate-y-1/2 w-[20vw] h-[50vh] bg-white/60 backdrop-blur-md shadow-xl p-2 bg-[url('./img/bg3.jpg')] bg-cover">
                    <div className='w-full h-[50%]'>
                        <img src="" alt="" />
                    </div>
                    <div className='text-white/90 pt-6'>
                        <h1 className='text-2xl font-bold leading-none'>Swift Digital <br /> Exchanges transactions</h1>
                        <p className='font-bold'>03</p>
                        <p className='text-[0.8vw]'>Trust Huobi for seamless and secure crypto transactions—where speed meets reliability.</p>
                    </div>
                </div>


            </div>
        </div>
    )
}

export default LandingPage
